{"name": "stock-app", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@mui/material": "^5.2.6", "axios": "^0.24.0", "highcharts": "^9.3.2", "highcharts-react-official": "^3.1.0", "next": "12.0.7", "react": "17.0.2", "react-dom": "17.0.2"}, "devDependencies": {"autoprefixer": "^10.4.0", "eslint": "8.5.0", "eslint-config-next": "12.0.7", "postcss": "^8.4.5", "tailwindcss": "^3.0.7"}}